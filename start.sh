#!/bin/bash

# MCP Shell Executor Server Startup Script

echo "🚀 Starting MCP Shell Executor Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "⚠️  Warning: Node.js version $NODE_VERSION detected. Recommended version is 18.0.0 or higher."
fi

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "📦 Building project..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ Build failed. Please check the errors above."
        exit 1
    fi
fi

# Start the server
echo "✅ Starting MCP Shell Executor Server..."
echo "🔒 Security features enabled"
echo "📝 Available tools:"
echo "   • execute-shell: Execute shell commands safely"
echo "   • get-system-info: Get system information"
echo "   • list-allowed-commands: Show security configuration"
echo ""
echo "💡 To stop the server, press Ctrl+C"
echo ""

npm start
