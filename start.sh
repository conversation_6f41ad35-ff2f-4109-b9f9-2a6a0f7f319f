#!/bin/bash

# MCP Shell Executor Server Startup Script

echo "🚀 Starting MCP Shell Executor Server..."

# Parse command line arguments
LAUNCH_INSPECTOR=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --test|--inspector|-t)
            LAUNCH_INSPECTOR=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --test, --inspector, -t    Launch MCP Inspector for testing"
            echo "  --help, -h                 Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                         Start server only"
            echo "  $0 --test                  Launch MCP Inspector with server"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "⚠️  Warning: Node.js version $NODE_VERSION detected. Recommended version is 18.0.0 or higher."
fi

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "📦 Building project..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ Build failed. Please check the errors above."
        exit 1
    fi
fi

# Function to open browser (cross-platform)
open_browser() {
    local url=$1
    if command -v open &> /dev/null; then
        # macOS
        open "$url"
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open "$url"
    elif command -v start &> /dev/null; then
        # Windows (Git Bash)
        start "$url"
    else
        echo "🌐 Please open your browser and go to: $url"
    fi
}

if [ "$INSPECTOR_ONLY" = true ]; then
    # Launch inspector only (recommended approach)
    echo "🧪 Launching MCP Inspector with integrated server..."
    echo "🔒 Security features enabled"
    echo "📝 Available tools:"
    echo "   • execute-shell: Execute shell commands safely"
    echo "   • get-system-info: Get system information"
    echo "   • list-allowed-commands: Show security configuration"
    echo ""
    echo "💡 The inspector will start the server automatically"
    echo "💡 To stop, press Ctrl+C"
    echo ""

    # Launch inspector with our server
    npx @modelcontextprotocol/inspector node dist/index.js

elif [ "$LAUNCH_INSPECTOR" = true ]; then
    # Start server in background and launch inspector
    echo "✅ Starting MCP Shell Executor Server in background..."
    echo "🔒 Security features enabled"

    # Start server in background
    npm start &
    SERVER_PID=$!

    # Wait a moment for server to start
    sleep 2

    echo "🧪 Launching MCP Inspector..."
    echo "📝 Available tools:"
    echo "   • execute-shell: Execute shell commands safely"
    echo "   • get-system-info: Get system information"
    echo "   • list-allowed-commands: Show security configuration"
    echo ""

    # Launch inspector
    npx @modelcontextprotocol/inspector node dist/index.js &
    INSPECTOR_PID=$!

    # Cleanup function
    cleanup() {
        echo ""
        echo "🛑 Shutting down..."
        if [ ! -z "$SERVER_PID" ]; then
            kill $SERVER_PID 2>/dev/null
        fi
        if [ ! -z "$INSPECTOR_PID" ]; then
            kill $INSPECTOR_PID 2>/dev/null
        fi
        exit 0
    }

    # Set up signal handlers
    trap cleanup SIGINT SIGTERM

    echo "💡 Press Ctrl+C to stop both server and inspector"

    # Wait for processes
    wait

else
    # Start server only
    echo "✅ Starting MCP Shell Executor Server..."
    echo "🔒 Security features enabled"
    echo "📝 Available tools:"
    echo "   • execute-shell: Execute shell commands safely"
    echo "   • get-system-info: Get system information"
    echo "   • list-allowed-commands: Show security configuration"
    echo ""
    echo "💡 To test with MCP Inspector, run: ./start.sh --test-only"
    echo "💡 To stop the server, press Ctrl+C"
    echo ""

    npm start
fi
