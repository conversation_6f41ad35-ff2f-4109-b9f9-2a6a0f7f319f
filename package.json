{"name": "mcp-shell-executor", "version": "1.0.0", "description": "MCP server for executing local shell commands", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "./test.sh", "test:inspector": "npx @modelcontextprotocol/inspector node dist/index.js"}, "keywords": ["mcp", "shell", "executor", "modelcontextprotocol"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}