{"name": "mcp-shell-executor", "version": "1.0.0", "description": "MCP server for executing local shell commands", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node --test"}, "keywords": ["mcp", "shell", "executor", "modelcontextprotocol"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.0", "zod": "^3.22.4"}, "engines": {"node": ">=18.0.0"}}