# MCP Shell Executor

一个基于 Model Context Protocol (MCP) 的安全 Shell 命令执行工具。

## 🚀 功能特性

- **安全执行**: 内置命令白名单和黑名单机制
- **超时控制**: 防止长时间运行的命令
- **输出限制**: 限制命令输出长度，防止内存溢出
- **工作目录控制**: 支持指定工作目录，但限制访问敏感系统目录
- **系统信息**: 获取基本系统信息
- **配置查看**: 查看当前安全配置

## 🛠️ 安装和使用

### 前置要求

- Node.js 18.0.0 或更高版本
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 构建项目

```bash
npm run build
```

### 启动服务器

```bash
# 使用启动脚本（推荐）
./start.sh

# 或直接运行
npm start

# 开发模式
npm run dev
```

## 🔧 可用工具

### 1. execute-shell

执行 shell 命令（带安全限制）

**参数:**
- `command` (string): 要执行的 shell 命令
- `workingDirectory` (string, 可选): 工作目录
- `timeout` (number, 可选): 超时时间（秒，最大30秒，默认10秒）

**示例:**
```json
{
  "command": "ls -la",
  "workingDirectory": "/home/<USER>",
  "timeout": 5
}
```

### 2. get-system-info

获取系统基本信息

**参数:** 无

### 3. list-allowed-commands

显示允许和禁止的命令列表

**参数:** 无

## 🔒 安全特性

### 允许的命令（白名单）
- 文件操作: `ls`, `pwd`, `cat`, `head`, `tail`, `grep`, `find`, `wc`, `sort`, `uniq`
- 系统信息: `date`, `whoami`, `uname`, `df`, `du`, `ps`, `top`, `which`, `whereis`
- 开发工具: `git`, `npm`, `node`, `python`, `python3`, `pip`, `pip3`
- 网络工具: `curl`, `wget`, `ping`, `nslookup`, `dig`

### 禁止的命令（黑名单）
- 文件删除/修改: `rm`, `rmdir`, `mv`, `cp`, `chmod`, `chown`
- 权限提升: `sudo`, `su`, `passwd`
- 进程控制: `kill`, `killall`, `pkill`
- 系统控制: `shutdown`, `reboot`, `halt`, `poweroff`
- 磁盘操作: `dd`, `fdisk`, `mkfs`, `mount`, `umount`, `format`

### 其他安全限制
- 禁止命令注入字符: `;`, `&`, `|`, `` ` ``, `$`, `()`, `{}`
- 禁止路径遍历: `..`
- 禁止重定向: `>`, `<<`
- 限制访问系统敏感目录: `/etc`, `/usr/bin`, `/usr/sbin`, `/bin`, `/sbin`
- 最大执行时间: 30秒
- 最大输出长度: 10,000 字节

## 📝 MCP 客户端配置

### Claude Desktop 配置

在 Claude Desktop 的配置文件中添加：

```json
{
  "mcpServers": {
    "shell-executor": {
      "command": "node",
      "args": ["/path/to/mcp-shell-executor/dist/index.js"],
      "cwd": "/path/to/mcp-shell-executor"
    }
  }
}
```

### 其他 MCP 客户端

使用 stdio 传输协议连接到服务器：

```bash
node dist/index.js
```

## 🧪 测试

### 基本测试

```bash
# 测试系统信息
echo '{"jsonrpc": "2.0", "id": 1, "method": "tools/call", "params": {"name": "get-system-info", "arguments": {}}}' | node dist/index.js

# 测试命令执行
echo '{"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "execute-shell", "arguments": {"command": "echo Hello World"}}}' | node dist/index.js

# 查看安全配置
echo '{"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "list-allowed-commands", "arguments": {}}}' | node dist/index.js
```

## ⚠️ 注意事项

1. **安全性**: 虽然内置了多重安全机制，但仍需谨慎使用，特别是在生产环境中
2. **权限**: 服务器以当前用户权限运行，无法执行需要更高权限的命令
3. **平台兼容性**: 主要在 Unix-like 系统（Linux, macOS）上测试，Windows 支持可能有限
4. **网络访问**: 某些网络命令可能需要适当的网络权限

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
