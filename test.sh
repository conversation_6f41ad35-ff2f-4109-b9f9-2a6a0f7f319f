#!/bin/bash

# Quick test script for MCP Shell Executor

echo "🧪 Quick Test - MCP Shell Executor"
echo "=================================="
echo ""

# Check if project is built
if [ ! -d "dist" ]; then
    echo "📦 Building project first..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ Build failed. Please check the errors above."
        exit 1
    fi
fi

echo "🚀 Launching MCP Inspector with Shell Executor..."
echo ""
echo "📋 Test Checklist:"
echo "  1. ✅ Try 'get-system-info' tool (no parameters needed)"
echo "  2. ✅ Try 'list-allowed-commands' tool (no parameters needed)"
echo "  3. ✅ Try 'execute-shell' with: {\"command\": \"ls -la\"}"
echo "  4. ✅ Try 'execute-shell' with: {\"command\": \"pwd\"}"
echo "  5. ✅ Try 'execute-shell' with: {\"command\": \"echo 'Hello MCP!'\"}"
echo "  6. ❌ Try 'execute-shell' with: {\"command\": \"rm -rf /\"} (should be blocked)"
echo ""
echo "💡 The web interface will open automatically"
echo "💡 Press Ctrl+C to stop the test"
echo ""

# Launch inspector (this includes the server)
npx @modelcontextprotocol/inspector node dist/index.js
